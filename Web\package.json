{"name": "xbt-web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@varlet/touch-emulator": "^3.10.3", "@varlet/ui": "^3.10.2", "@zxing/library": "^0.21.3", "axios": "^1.8.4", "dayjs": "^1.11.13", "encrypt": "^0.0.1", "jsencrypt": "^3.3.2", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "typescript": "^5.8.3", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}