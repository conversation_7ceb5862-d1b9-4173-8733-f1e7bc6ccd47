{"buildFiles": ["/opt/homebrew/Cellar/dart/3.5.3/libexec/lib/core/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/Python/XBT/Client/android/app/.cxx/Debug/3e4e6p4h/x86_64", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/Python/XBT/Client/android/app/.cxx/Debug/3e4e6p4h/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}