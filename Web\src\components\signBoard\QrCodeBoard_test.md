# 智能扫码签到功能测试说明

## 功能概述
实现了智能的扫码签到功能，具有以下特性：
1. **实时监控二维码更新**：持续监控二维码内容变化
2. **智能签到逻辑**：每次签到使用最新的二维码enc值
3. **重试机制**：最多3次重试，每次重试都获取最新二维码数据
4. **错误处理**：完善的错误提示和状态反馈

## 核心改进

### QrCodeBoard.vue 改进
- 新增 `enableSmartMonitoring` 属性控制智能监控模式
- 实时跟踪二维码数据变化，记录历史
- 提供 `getLatestQrData()` 方法获取最新二维码数据
- 暴露组件方法给父组件调用

### SignProgressDialog.vue 改进
- 新增 `qrCodeBoardRef` 属性接收二维码组件引用
- 实现 `performSmartSign()` 智能签到函数
- 支持最多3次重试，每次重试获取最新二维码数据
- 渐进式重试延迟（1秒、2秒、3秒）

### SignDetailView.vue 改进
- 传递二维码组件引用给签到进度对话框
- 自动检测二维码签到类型并启用智能监控

## 测试场景

### 场景1：正常二维码签到
1. 打开二维码签到页面
2. 扫描二维码
3. 选择要代签的同学
4. 点击签到
5. 验证签到成功

### 场景2：二维码过期重试
1. 扫描二维码后等待8-12秒（模拟二维码过期）
2. 开始签到流程
3. 第一次签到失败（二维码过期）
4. 系统自动重新扫描获取新二维码
5. 重试签到成功

### 场景3：多次重试失败
1. 在网络不稳定环境下进行签到
2. 观察系统进行最多3次重试
3. 每次重试都使用最新二维码数据
4. 最终失败时显示详细错误信息

## 关键技术点

### 二维码数据结构
```javascript
{
  enc: "加密字符串",
  c: "课程标识",
  timestamp: 时间戳,
  rawText: "完整二维码文本"
}
```

### 智能重试逻辑
- 最大重试次数：3次
- 重试间隔：递增延迟（1s, 2s, 3s）
- 每次重试都获取最新二维码数据
- 详细的日志记录和错误处理

### 组件通信
- 父组件通过 ref 获取子组件实例
- 子组件暴露方法供父组件调用
- 事件发射机制通知二维码更新

## 使用方法

### 启用智能监控模式
```vue
<QrCodeBoard 
  :signCallBack="signCallBack"
  :enableSmartMonitoring="true"
  ref="qrCodeBoard"
/>
```

### 获取最新二维码数据
```javascript
const latestData = qrCodeBoardRef.value.getLatestQrData();
```

### 智能签到调用
```javascript
const result = await performSmartSign(studentData);
```

## 注意事项
1. 确保摄像头权限已授权
2. 二维码需要清晰可见
3. 网络连接稳定
4. 浏览器支持现代Web API

## 兼容性
- 保持向后兼容，不影响现有功能
- 智能监控模式可选择性启用
- 传统扫码模式仍然可用
