{"buildFiles": ["/opt/homebrew/Cellar/dart/3.5.3/libexec/lib/core/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/Python/学不通/Client/android/app/.cxx/RelWithDebInfo/3m6z485p/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/Python/学不通/Client/android/app/.cxx/RelWithDebInfo/3m6z485p/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}