# 扫码签到位置功能界面说明

## 界面布局

```
┌─────────────────────────────────────────┐
│ 🔘 添加位置信息                           │ ← 位置开关
│ 📍 综合实验楼                    ▶       │ ← 位置选择器（开启时显示）
├─────────────────────────────────────────┤
│                                       │
│              摄像头画面                  │
│                                       │
│  ┌─────────────────────────────────┐    │
│  │ 缩放 ████████████████ 2.1x    │    │ ← 缩放控制（支持时显示）
│  └─────────────────────────────────┘    │
│                                       │
│                                       │
│                                       │
│                                       │
│                                       │
│            ┌─────────────┐             │
│            │ 扫码成功     │             │ ← 状态提示
│            └─────────────┘             │
└─────────────────────────────────────────┘
```

## 功能状态

### 1. 默认状态（位置功能关闭）
```
┌─────────────────────────────────────────┐
│ ⚪ 添加位置信息                           │
├─────────────────────────────────────────┤
│              摄像头画面                  │
│                                       │
│            ┌─────────────┐             │
│            │ 正在尝试识别... │           │
│            └─────────────┘             │
└─────────────────────────────────────────┘
```

### 2. 位置功能开启但未选择
```
┌─────────────────────────────────────────┐
│ 🔘 添加位置信息                           │
│ 📍 点击选择位置                    ▶     │
├─────────────────────────────────────────┤
│              摄像头画面                  │
│                                       │
│            ┌─────────────┐             │
│            │ 正在尝试识别... │           │
│            └─────────────┘             │
└─────────────────────────────────────────┘
```

### 3. 位置功能开启且已选择
```
┌─────────────────────────────────────────┐
│ 🔘 添加位置信息                           │
│ 📍 综合实验楼                    ▶       │
├─────────────────────────────────────────┤
│              摄像头画面                  │
│                                       │
│            ┌─────────────┐             │
│            │ 扫码成功     │             │
│            └─────────────┘             │
└─────────────────────────────────────────┘
```

## 位置选择器界面

当用户点击位置选择器时，会弹出选择对话框：

```
┌─────────────────────────────────────────┐
│              选择签到地点                 │
├─────────────────────────────────────────┤
│                                       │
│  ⚪ 综合实验楼                           │
│  🔘 B楼                               │ ← 当前选中
│  ⚪ A楼                               │
│  ⚪ 图书馆                             │
│  ⚪ 体育馆                             │
│                                       │
├─────────────────────────────────────────┤
│        [取消]        [确定]             │
└─────────────────────────────────────────┘
```

## 数据流程图

```
用户操作流程：
┌─────────┐    ┌─────────┐    ┌─────────┐    ┌─────────┐
│ 开启位置  │ → │ 选择位置  │ → │ 扫描二维码 │ → │ 自动签到  │
│ 开关    │    │ 信息    │    │        │    │        │
└─────────┘    └─────────┘    └─────────┘    └─────────┘
     │              │              │              │
     ▼              ▼              ▼              ▼
启用位置功能    选择具体位置    获取二维码数据    合并数据发送

数据合并过程：
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ 二维码数据    │    │ 位置数据      │    │ 最终签到数据  │
│ {enc, c}    │ +  │ {location}  │ =  │ {enc, c,    │
│            │    │            │    │  location}  │
└─────────────┘    └─────────────┘    └─────────────┘
```

## 样式特点

### 视觉设计
- **渐变背景**：顶部区域使用黑色渐变，确保文字清晰可见
- **毛玻璃效果**：位置选择器使用半透明背景和模糊效果
- **现代化图标**：使用 Varlet UI 的图标系统
- **响应式交互**：悬停和点击时的平滑过渡效果

### 布局特点
- **层级分明**：位置选择 > 缩放控制 > 状态提示
- **空间优化**：合理利用摄像头画面上方空间
- **不遮挡扫码**：确保核心扫码功能不受影响
- **自适应布局**：支持不同屏幕尺寸

## 交互说明

### 位置开关
- **默认状态**：关闭（⚪）
- **开启状态**：蓝色（🔘）
- **切换效果**：平滑的开关动画

### 位置选择器
- **未选择状态**：显示"点击选择位置"
- **已选择状态**：显示具体位置名称
- **点击效果**：轻微的向上移动和背景变亮
- **选择器弹窗**：使用 Varlet UI 的 Picker 组件

### 状态反馈
- **扫码状态**：实时显示扫码进度
- **位置状态**：选择位置后立即更新显示
- **错误提示**：位置选择失败时的友好提示

## 技术细节

### CSS 层级管理
```css
.location-control { z-index: 113; }  /* 位置选择区域 */
.zoom-control { z-index: 112; }      /* 缩放控制 */
.tip { z-index: 111; }               /* 状态提示 */
```

### 响应式适配
```css
@media (max-width: 480px) {
  .location-control { padding: 12px; }
  .location-label { font-size: 12px; }
}
```

### 动画效果
```css
.location-selector {
  transition: all 0.2s ease;
}
.location-selector:hover {
  transform: translateY(-1px);
}
```

这个设计确保了功能的完整性和用户体验的优秀性，同时保持了与现有界面的一致性。
