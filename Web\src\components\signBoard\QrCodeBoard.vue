<template>
  <div class="bg">
    <video ref="video" id="video" autoplay>
    </video>
    <div class="tip">{{ form.tipMsg }}</div>

    <!-- 缩放控制滑杆 -->
    <div class="zoom-control" v-if="zoomSupported">
      <div class="zoom-slider-container">
        <div class="zoom-label">缩放</div>
        <input
          type="range"
          class="zoom-slider"
          :min="zoomCapabilities.min"
          :max="zoomCapabilities.max"
          :step="zoomCapabilities.step"
          v-model="currentZoom"
          @input="onZoomChange"
        />
        <div class="zoom-value">{{ Math.round(currentZoom * 10) / 10 }}x</div>
      </div>
    </div>
  </div>

</template>
<script setup>
import { ref, onUnmounted, reactive, onMounted, watch } from 'vue'
import { BrowserMultiFormatReader } from '@zxing/library'
import { Snackbar } from '@varlet/ui'

const props = defineProps({
  signCallBack: {
    type: Function,
    default: (data) => { },
  },
  // 新增：是否启用智能监控模式
  enableSmartMonitoring: {
    type: Boolean,
    default: false,
  }
})

// 新增：暴露给父组件的方法
const emit = defineEmits(['qrCodeUpdated', 'latestQrData'])

const form = reactive({
  tipMsg: '尝试识别中...'
})

// 缩放相关状态
const video = ref(null)
const currentZoom = ref(1)
const zoomSupported = ref(false)
const zoomCapabilities = ref({
  min: 1,
  max: 3,
  step: 0.1
})
const videoTrack = ref(null)

// 新增：智能监控相关状态
const latestQrData = ref(null)
const qrHistory = ref([])
const monitoringInterval = ref(null)
const isMonitoring = ref(false)

function onScaned(text) {
  if (!text.includes("mobilelearn.chaoxing.com")) {
    form.tipMsg = '请扫描学习通二维码'
    return
  }

  const enc = text.split('&enc=')[1].split('&')[0];
  const c = text.split('&c=')[1].split('&')[0];
  const timestamp = Date.now();

  const newQrData = { enc, c, timestamp, rawText: text };

  // 检查是否是新的二维码数据
  if (!latestQrData.value || latestQrData.value.enc !== enc) {
    latestQrData.value = newQrData;
    qrHistory.value.push(newQrData);

    // 保持历史记录在合理范围内
    if (qrHistory.value.length > 10) {
      qrHistory.value = qrHistory.value.slice(-10);
    }

    form.tipMsg = '检测到新二维码'

    // 通知父组件二维码已更新
    emit('qrCodeUpdated', newQrData);

    console.log('二维码更新:', { enc, timestamp: new Date(timestamp).toLocaleTimeString() });
  }

  // 如果不是智能监控模式，执行原有的回调逻辑
  if (!props.enableSmartMonitoring) {
    form.tipMsg = '扫码成功'
    props.signCallBack({ enc, c })
  }
}

// 新增：获取最新二维码数据的方法
function getLatestQrData() {
  return latestQrData.value;
}

// 新增：开始智能监控
function startSmartMonitoring() {
  if (isMonitoring.value) return;

  isMonitoring.value = true;
  form.tipMsg = '智能监控中...';

  console.log('开始智能二维码监控');
}

// 新增：停止智能监控
function stopSmartMonitoring() {
  if (!isMonitoring.value) return;

  isMonitoring.value = false;
  form.tipMsg = '监控已停止';

  console.log('停止智能二维码监控');
}

// 暴露方法给父组件
defineExpose({
  getLatestQrData,
  startSmartMonitoring,
  stopSmartMonitoring,
  latestQrData
})

// 缩放变化处理
const onZoomChange = async () => {
  if (!videoTrack.value || !zoomSupported.value) return

  try {
    await videoTrack.value.applyConstraints({
      advanced: [{
        zoom: currentZoom.value
      }]
    })
  } catch (error) {
    console.warn('缩放调整失败:', error)
  }
}

// 检查并设置缩放功能
const checkZoomSupport = async (track) => {
  try {
    const capabilities = track.getCapabilities()
    if (capabilities.zoom) {
      zoomSupported.value = true
      zoomCapabilities.value = {
        min: capabilities.zoom.min || 1,
        max: capabilities.zoom.max || 3,
        step: capabilities.zoom.step || 0.1
      }
      currentZoom.value = capabilities.zoom.min || 1
      videoTrack.value = track
    }
  } catch (error) {
    console.warn('无法获取缩放功能:', error)
    zoomSupported.value = false
  }
}

const codeReader = new BrowserMultiFormatReader()
const openScan = () => {
  codeReader
    .getVideoInputDevices()
    .then(async (videoInputDevices) => {
      form.tipMsg = '正在调用摄像头...'
      let firstDeviceId = videoInputDevices[0].deviceId
      // 获取第一个摄像头设备的名称
      const videoInputDeviceslablestr = JSON.stringify(videoInputDevices[0].label)
      if (videoInputDevices.length > 1) {
        if (videoInputDeviceslablestr.indexOf('back') > -1) {
          firstDeviceId = videoInputDevices[0].deviceId
        } else {
          firstDeviceId = videoInputDevices[1].deviceId
        }
      }
      await decodeFromInputVideoFunc(firstDeviceId)
    })
    .catch((err) => {
      form.tipMsg = '请检查摄像头(权限)是否正常'
    })
}

const decodeFromInputVideoFunc = async (firstDeviceId) => {
  codeReader.reset() // 重置

  // 先获取媒体流以检查缩放支持
  try {
    const stream = await navigator.mediaDevices.getUserMedia({
      video: {
        deviceId: firstDeviceId,
        width: { ideal: 1280 },
        height: { ideal: 720 }
      }
    })

    const videoTrackTemp = stream.getVideoTracks()[0]
    await checkZoomSupport(videoTrackTemp)

    // 设置视频源
    if (video.value) {
      video.value.srcObject = stream
    }
  } catch (error) {
    console.warn('获取媒体流失败:', error)
  }

  codeReader.decodeFromInputVideoDeviceContinuously(firstDeviceId, 'video', (result, err) => {
    form.tipMsg = '正在尝试识别...' // 提示信息
    if (result) {
      onScaned(result.getText())
    }
    if (err && !err) {
      form.tipMsg = '识别失败'
    }
  })
}
//销毁组件
onUnmounted(() => {
  if (videoTrack.value) {
    videoTrack.value.stop()
  }
  codeReader.reset();
  codeReader.stopContinuousDecode();
})

onMounted(() => {
  openScan() // 调用扫码方法
})

</script>
<style scoped>
.bg {
  aspect-ratio: 1;
  width: 100%;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

#video {
  object-fit: cover;
  width: 100%;
  height: 100%;
  background: #000;
}

.tip {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  font-size: 16px;
  text-align: center;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8);
  z-index: 111;
  background: rgba(0, 0, 0, 0.5);
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.zoom-control {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  z-index: 112;
}

.zoom-slider-container {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 25px;
  padding: 12px 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.zoom-label {
  color: white;
  font-size: 14px;
  font-weight: 500;
  margin-right: 12px;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
  white-space: nowrap;
}

.zoom-slider {
  flex: 1;
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  outline: none;
  margin: 0 12px;
}

.zoom-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: #ffffff;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.8);
  transition: all 0.2s ease;
}

.zoom-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
}

.zoom-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: #ffffff;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.8);
  transition: all 0.2s ease;
}

.zoom-value {
  color: white;
  font-size: 14px;
  font-weight: 600;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
  white-space: nowrap;
  min-width: 35px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .zoom-control {
    top: 15px;
    left: 15px;
    right: 15px;
  }

  .zoom-slider-container {
    padding: 10px 16px;
  }

  .zoom-label, .zoom-value {
    font-size: 12px;
  }

  .tip {
    bottom: 15px;
    font-size: 14px;
    padding: 6px 12px;
  }
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .zoom-slider-container {
    background: rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}
</style>