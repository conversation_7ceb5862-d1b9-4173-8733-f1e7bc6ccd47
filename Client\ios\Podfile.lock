PODS:
  - Flutter (1.0.0)
  - MTBBarcodeScanner (5.0.11)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - restart_app (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - restart_app (from `.symlinks/plugins/restart_app/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)

SPEC REPOS:
  trunk:
    - MTBBarcodeScanner

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  restart_app:
    :path: ".symlinks/plugins/restart_app/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  qr_code_scanner: bb67d64904c3b9658ada8c402e8b4d406d5d796e
  restart_app: 806659942bf932f6ce51c5372f91ce5e81c8c14a
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78

PODFILE CHECKSUM: 819463e6a0290f5a72f145ba7cde16e8b6ef0796

COCOAPODS: 1.15.2
