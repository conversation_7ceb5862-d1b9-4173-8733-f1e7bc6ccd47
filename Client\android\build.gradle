allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

buildscript {
    ext.kotlin_version = '1.8.10'
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        // 修改 Gradle 插件版本
        classpath 'com.android.tools.build:gradle:4.2.0'  
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
    afterEvaluate {
        // check if android block is available

        if (it.hasProperty('android')) {

            if (it.android.namespace == null) {
                def manifest = new XmlSlurper().parse(file(it.android.sourceSets.main.manifest.srcFile))
                def packageName = <EMAIL>()
                println("Setting ${packageName} as android namespace")
                android.namespace = packageName
            }

            def javaVersion = JavaVersion.VERSION_1_8
            android {
                def androidApiVersion = 34
                compileSdkVersion androidApiVersion
                defaultConfig {
                    targetSdkVersion androidApiVersion
                }
                compileOptions {
                    sourceCompatibility javaVersion
                    targetCompatibility javaVersion
                }
                tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
                    kotlinOptions {
                        jvmTarget = javaVersion.toString()
                    }
                }
                println("Setting java version to ${javaVersion.toString()} which is $javaVersion")
                println("Setting compileSdkVersion and targetSdkVersion to $androidApiVersion")
            }
        }
    }
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
